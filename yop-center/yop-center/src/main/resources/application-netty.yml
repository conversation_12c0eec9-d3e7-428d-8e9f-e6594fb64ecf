# YOP Gateway Netty Configuration
# 改进后的Netty网关配置示例

yop:
  gateway:
    netty:
      # 基础服务器配置
      port: 8080
      
      # 线程配置 - 改进的动态线程池
      boss-threads: 1
      worker-threads: 16  # CPU核数 * 2
      business-threads: 32  # CPU核数 * 4，核心线程数
      business-max-threads: 64  # 最大线程数，支持动态扩展
      business-queue-size: 10000
      business-thread-keep-alive-seconds: 60
      allow-core-thread-timeout: true
      
      # 网络参数
      so-backlog: 1024
      so-reuse-addr: true
      tcp-no-delay: true
      so-keep-alive: true
      connect-timeout-millis: 30000
      read-timeout-seconds: 60
      write-timeout-seconds: 60
      
      # 内容处理
      max-content-length: 10485760  # 10MB
      compression-enabled: true
      compression-threshold: 1024
      
      # 性能优化
      use-direct-memory: true
      epoll-enabled: true  # Linux系统启用Epoll
      
      # 优雅关闭
      graceful-shutdown-timeout-seconds: 10
      
      # 监控配置
      metrics-enabled: true
      metrics-interval-seconds: 30

# 背压控制配置
backpressure:
  # 并发控制
  max-concurrent-requests: 1000
  max-queued-requests: 2000
  max-requests-per-second: 10000
  
  # 系统资源阈值
  cpu-threshold: 0.8
  memory-threshold: 0.8
  
  # 监控配置
  resource-check-interval-seconds: 5

# 异步过滤器链配置
async-filter-chain:
  # 性能阈值
  max-active-requests: 1000
  max-queued-requests: 2000
  max-filter-execution-time: 5000  # 5秒
  
  # 并行执行配置
  enable-parallel-execution: true
  parallel-execution-threshold: 3  # 超过3个过滤器时启用并行执行

# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,info
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true

# 日志配置
logging:
  level:
    com.yeepay.g3.yop.gateway.netty: INFO
    com.yeepay.g3.yop.gateway.async: DEBUG
    io.netty: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{requestId}] %logger{36} - %msg%n"

# JVM 优化建议（通过启动参数设置）
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+UseStringDeduplication
# -XX:+OptimizeStringConcat
# -Dio.netty.allocator.type=pooled
# -Dio.netty.allocator.numDirectArenas=16
# -Dio.netty.allocator.numHeapArenas=16
# -Dio.netty.allocator.pageSize=8192
# -Dio.netty.allocator.maxOrder=11
