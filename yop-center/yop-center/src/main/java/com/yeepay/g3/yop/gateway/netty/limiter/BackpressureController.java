/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.limiter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * title: 背压控制器<br/>
 * description: 实现系统背压控制，防止过载并提供限流功能<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class BackpressureController {

    // 配置参数
    private volatile long maxConcurrentRequests = 1000;
    private volatile long maxQueuedRequests = 2000;
    private volatile long maxRequestsPerSecond = 10000;
    private volatile double cpuThreshold = 0.8;
    private volatile double memoryThreshold = 0.8;

    // 运行时统计
    private final AtomicLong currentConcurrentRequests = new AtomicLong(0);
    private final AtomicLong currentQueuedRequests = new AtomicLong(0);
    private final LongAdder totalRejectedRequests = new LongAdder();
    private final LongAdder totalAcceptedRequests = new LongAdder();

    // 限流相关
    private final AtomicLong lastResetTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong requestsInCurrentSecond = new AtomicLong(0);

    // 系统资源监控
    private volatile double currentCpuUsage = 0.0;
    private volatile double currentMemoryUsage = 0.0;
    private volatile long lastResourceCheckTime = System.currentTimeMillis();

    /**
     * 检查是否可以接受新请求
     */
    public boolean canAcceptRequest() {
        // 更新系统资源使用情况
        updateSystemResources();

        // 检查并发请求数限制
        if (currentConcurrentRequests.get() >= maxConcurrentRequests) {
            log.debug("Rejecting request: concurrent requests limit exceeded ({}/{})", 
                    currentConcurrentRequests.get(), maxConcurrentRequests);
            totalRejectedRequests.increment();
            return false;
        }

        // 检查队列长度限制
        if (currentQueuedRequests.get() >= maxQueuedRequests) {
            log.debug("Rejecting request: queue limit exceeded ({}/{})", 
                    currentQueuedRequests.get(), maxQueuedRequests);
            totalRejectedRequests.increment();
            return false;
        }

        // 检查QPS限制
        if (!checkRateLimit()) {
            log.debug("Rejecting request: rate limit exceeded");
            totalRejectedRequests.increment();
            return false;
        }

        // 检查系统资源使用情况
        if (currentCpuUsage > cpuThreshold) {
            log.warn("Rejecting request: CPU usage too high ({:.2f}% > {:.2f}%)", 
                    currentCpuUsage * 100, cpuThreshold * 100);
            totalRejectedRequests.increment();
            return false;
        }

        if (currentMemoryUsage > memoryThreshold) {
            log.warn("Rejecting request: Memory usage too high ({:.2f}% > {:.2f}%)", 
                    currentMemoryUsage * 100, memoryThreshold * 100);
            totalRejectedRequests.increment();
            return false;
        }

        // 通过所有检查，接受请求
        totalAcceptedRequests.increment();
        return true;
    }

    /**
     * 记录请求开始处理
     */
    public void onRequestStart() {
        currentConcurrentRequests.incrementAndGet();
    }

    /**
     * 记录请求处理完成
     */
    public void onRequestComplete() {
        currentConcurrentRequests.decrementAndGet();
    }

    /**
     * 记录请求进入队列
     */
    public void onRequestQueued() {
        currentQueuedRequests.incrementAndGet();
    }

    /**
     * 记录请求离开队列
     */
    public void onRequestDequeued() {
        currentQueuedRequests.decrementAndGet();
    }

    /**
     * 检查QPS限制
     */
    private boolean checkRateLimit() {
        long currentTime = System.currentTimeMillis();
        long lastReset = lastResetTime.get();

        // 如果超过1秒，重置计数器
        if (currentTime - lastReset >= 1000) {
            if (lastResetTime.compareAndSet(lastReset, currentTime)) {
                requestsInCurrentSecond.set(0);
            }
        }

        // 检查当前秒内的请求数
        long currentRequests = requestsInCurrentSecond.incrementAndGet();
        return currentRequests <= maxRequestsPerSecond;
    }

    /**
     * 更新系统资源使用情况
     */
    private void updateSystemResources() {
        long currentTime = System.currentTimeMillis();
        
        // 每5秒更新一次资源使用情况
        if (currentTime - lastResourceCheckTime >= 5000) {
            lastResourceCheckTime = currentTime;
            
            try {
                // 获取CPU使用率（简化实现）
                Runtime runtime = Runtime.getRuntime();
                long totalMemory = runtime.totalMemory();
                long freeMemory = runtime.freeMemory();
                long usedMemory = totalMemory - freeMemory;
                long maxMemory = runtime.maxMemory();
                
                currentMemoryUsage = (double) usedMemory / maxMemory;
                
                // CPU使用率需要更复杂的实现，这里简化处理
                // 可以集成JMX或其他监控工具来获取准确的CPU使用率
                currentCpuUsage = Math.min(0.9, currentConcurrentRequests.get() / (double) maxConcurrentRequests);
                
            } catch (Exception e) {
                log.warn("Error updating system resources", e);
            }
        }
    }

    /**
     * 获取当前状态
     */
    public BackpressureStatus getStatus() {
        BackpressureStatus status = new BackpressureStatus();
        status.setMaxConcurrentRequests(maxConcurrentRequests);
        status.setCurrentConcurrentRequests(currentConcurrentRequests.get());
        status.setMaxQueuedRequests(maxQueuedRequests);
        status.setCurrentQueuedRequests(currentQueuedRequests.get());
        status.setMaxRequestsPerSecond(maxRequestsPerSecond);
        status.setRequestsInCurrentSecond(requestsInCurrentSecond.get());
        status.setTotalRejectedRequests(totalRejectedRequests.sum());
        status.setTotalAcceptedRequests(totalAcceptedRequests.sum());
        status.setCpuUsage(currentCpuUsage);
        status.setMemoryUsage(currentMemoryUsage);
        status.setCpuThreshold(cpuThreshold);
        status.setMemoryThreshold(memoryThreshold);
        
        // 计算拒绝率
        long totalRequests = status.getTotalAcceptedRequests() + status.getTotalRejectedRequests();
        if (totalRequests > 0) {
            status.setRejectionRate((double) status.getTotalRejectedRequests() / totalRequests);
        }
        
        return status;
    }

    /**
     * 重置统计信息
     */
    public void reset() {
        currentConcurrentRequests.set(0);
        currentQueuedRequests.set(0);
        totalRejectedRequests.reset();
        totalAcceptedRequests.reset();
        requestsInCurrentSecond.set(0);
        lastResetTime.set(System.currentTimeMillis());
        log.info("BackpressureController statistics reset");
    }

    /**
     * 动态调整配置
     */
    public void updateConfiguration(long maxConcurrent, long maxQueued, long maxRps, 
                                  double cpuThresh, double memThresh) {
        this.maxConcurrentRequests = maxConcurrent;
        this.maxQueuedRequests = maxQueued;
        this.maxRequestsPerSecond = maxRps;
        this.cpuThreshold = cpuThresh;
        this.memoryThreshold = memThresh;
        
        log.info("BackpressureController configuration updated: maxConcurrent={}, maxQueued={}, " +
                "maxRps={}, cpuThreshold={:.2f}, memoryThreshold={:.2f}", 
                maxConcurrent, maxQueued, maxRps, cpuThresh, memThresh);
    }

    /**
     * 背压状态信息
     */
    public static class BackpressureStatus {
        private long maxConcurrentRequests;
        private long currentConcurrentRequests;
        private long maxQueuedRequests;
        private long currentQueuedRequests;
        private long maxRequestsPerSecond;
        private long requestsInCurrentSecond;
        private long totalRejectedRequests;
        private long totalAcceptedRequests;
        private double rejectionRate;
        private double cpuUsage;
        private double memoryUsage;
        private double cpuThreshold;
        private double memoryThreshold;

        // Getters and Setters
        public long getMaxConcurrentRequests() { return maxConcurrentRequests; }
        public void setMaxConcurrentRequests(long maxConcurrentRequests) { this.maxConcurrentRequests = maxConcurrentRequests; }
        
        public long getCurrentConcurrentRequests() { return currentConcurrentRequests; }
        public void setCurrentConcurrentRequests(long currentConcurrentRequests) { this.currentConcurrentRequests = currentConcurrentRequests; }
        
        public long getMaxQueuedRequests() { return maxQueuedRequests; }
        public void setMaxQueuedRequests(long maxQueuedRequests) { this.maxQueuedRequests = maxQueuedRequests; }
        
        public long getCurrentQueuedRequests() { return currentQueuedRequests; }
        public void setCurrentQueuedRequests(long currentQueuedRequests) { this.currentQueuedRequests = currentQueuedRequests; }
        
        public long getMaxRequestsPerSecond() { return maxRequestsPerSecond; }
        public void setMaxRequestsPerSecond(long maxRequestsPerSecond) { this.maxRequestsPerSecond = maxRequestsPerSecond; }
        
        public long getRequestsInCurrentSecond() { return requestsInCurrentSecond; }
        public void setRequestsInCurrentSecond(long requestsInCurrentSecond) { this.requestsInCurrentSecond = requestsInCurrentSecond; }
        
        public long getTotalRejectedRequests() { return totalRejectedRequests; }
        public void setTotalRejectedRequests(long totalRejectedRequests) { this.totalRejectedRequests = totalRejectedRequests; }
        
        public long getTotalAcceptedRequests() { return totalAcceptedRequests; }
        public void setTotalAcceptedRequests(long totalAcceptedRequests) { this.totalAcceptedRequests = totalAcceptedRequests; }
        
        public double getRejectionRate() { return rejectionRate; }
        public void setRejectionRate(double rejectionRate) { this.rejectionRate = rejectionRate; }
        
        public double getCpuUsage() { return cpuUsage; }
        public void setCpuUsage(double cpuUsage) { this.cpuUsage = cpuUsage; }
        
        public double getMemoryUsage() { return memoryUsage; }
        public void setMemoryUsage(double memoryUsage) { this.memoryUsage = memoryUsage; }
        
        public double getCpuThreshold() { return cpuThreshold; }
        public void setCpuThreshold(double cpuThreshold) { this.cpuThreshold = cpuThreshold; }
        
        public double getMemoryThreshold() { return memoryThreshold; }
        public void setMemoryThreshold(double memoryThreshold) { this.memoryThreshold = memoryThreshold; }
    }
}
