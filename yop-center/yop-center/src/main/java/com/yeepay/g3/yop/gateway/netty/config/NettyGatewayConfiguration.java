/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.config;

import com.yeepay.g3.yop.gateway.netty.handler.YopGatewayHandler;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.server.YopGatewayServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * title: Netty网关配置<br/>
 * description: Netty网关相关Bean的配置类<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Configuration
public class NettyGatewayConfiguration {

    @Bean
    public NettyServerConfig nettyServerConfig() {
        return new NettyServerConfig();
    }

    @Bean
    public ExecutorService businessExecutor(NettyServerConfig config) {
        // 支持动态线程池配置
        int coreThreads = config.getBusinessThreads();
        int maxThreads = Math.max(coreThreads, config.getBusinessMaxThreads());

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                coreThreads,
                maxThreads,
                config.getBusinessThreadKeepAliveSeconds(),
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(config.getBusinessQueueSize()),
                r -> {
                    Thread t = new Thread(r, "yop-business-" + System.nanoTime());
                    t.setDaemon(false);
                    t.setUncaughtExceptionHandler((thread, ex) ->
                        log.error("Uncaught exception in business thread: {}", thread.getName(), ex));
                    return t;
                },
                // 使用自定义拒绝策略，提供更好的背压控制
                new ThreadPoolExecutor.AbortPolicy()
        );

        // 允许核心线程超时，提高资源利用率
        executor.allowCoreThreadTimeOut(config.isAllowCoreThreadTimeout());

        log.info("Business thread pool created: core={}, max={}, queue={}, keepAlive={}s",
                coreThreads, maxThreads, config.getBusinessQueueSize(),
                config.getBusinessThreadKeepAliveSeconds());

        return executor;
    }

    @Bean
    public NettyServerMetrics nettyServerMetrics() {
        return new NettyServerMetrics();
    }

    @Bean
    public YopGatewayHandler yopGatewayHandler(ExecutorService businessExecutor, 
                                               NettyServerMetrics metrics) {
        YopGatewayHandler handler = new YopGatewayHandler();
        handler.setBusinessExecutor(businessExecutor);
        handler.setMetrics(metrics);
        return handler;
    }

    @Bean
    public YopGatewayServer yopGatewayServer(NettyServerConfig config,
                                             YopGatewayHandler gatewayHandler) {
        YopGatewayServer server = new YopGatewayServer();
        server.setConfig(config);
        server.setGatewayHandler(gatewayHandler);
        return server;
    }

}