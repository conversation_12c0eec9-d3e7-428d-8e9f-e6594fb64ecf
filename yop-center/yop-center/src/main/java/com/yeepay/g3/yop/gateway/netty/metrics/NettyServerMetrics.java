/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.metrics;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * title: Netty服务器性能监控<br/>
 * description: 收集和统计Netty服务器的性能指标<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class NettyServerMetrics {

    /**
     * 总请求数
     */
    private final LongAdder totalRequests = new LongAdder();

    /**
     * 成功请求数
     */
    private final LongAdder successRequests = new LongAdder();

    /**
     * 失败请求数
     */
    private final LongAdder failedRequests = new LongAdder();

    /**
     * 活跃连接数
     */
    private final AtomicLong activeConnections = new AtomicLong(0);

    /**
     * 总连接数
     */
    private final LongAdder totalConnections = new LongAdder();

    /**
     * 当前QPS
     */
    private final AtomicLong currentQps = new AtomicLong(0);

    /**
     * 平均响应时间
     */
    private final AtomicLong averageResponseTime = new AtomicLong(0);

    /**
     * 最大响应时间
     */
    private final AtomicLong maxResponseTime = new AtomicLong(0);

    /**
     * 最小响应时间
     */
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);

    /**
     * 响应时间总计
     */
    private final LongAdder totalResponseTime = new LongAdder();

    /**
     * 上次统计时间
     */
    private volatile long lastStatTime = System.currentTimeMillis();

    /**
     * 上次请求数
     */
    private volatile long lastRequestCount = 0;

    @PostConstruct
    public void init() {
        log.info("NettyServerMetrics initialized");
    }

    /**
     * 记录请求开始
     */
    public void recordRequestStart() {
        totalRequests.increment();
    }

    /**
     * 记录请求成功
     */
    public void recordRequestSuccess(long responseTimeMs) {
        successRequests.increment();
        recordResponseTime(responseTimeMs);
    }

    /**
     * 记录请求失败
     */
    public void recordRequestFailure(long responseTimeMs) {
        failedRequests.increment();
        recordResponseTime(responseTimeMs);
    }

    /**
     * 记录响应时间
     */
    private void recordResponseTime(long responseTimeMs) {
        totalResponseTime.add(responseTimeMs);
        
        // 更新最大响应时间
        long currentMax = maxResponseTime.get();
        while (responseTimeMs > currentMax) {
            if (maxResponseTime.compareAndSet(currentMax, responseTimeMs)) {
                break;
            }
            currentMax = maxResponseTime.get();
        }
        
        // 更新最小响应时间
        long currentMin = minResponseTime.get();
        while (responseTimeMs < currentMin) {
            if (minResponseTime.compareAndSet(currentMin, responseTimeMs)) {
                break;
            }
            currentMin = minResponseTime.get();
        }
        
        // 计算平均响应时间
        long totalCount = successRequests.sum() + failedRequests.sum();
        if (totalCount > 0) {
            averageResponseTime.set(totalResponseTime.sum() / totalCount);
        }
    }

    /**
     * 记录连接建立
     */
    public void recordConnectionEstablished() {
        activeConnections.incrementAndGet();
        totalConnections.increment();
    }

    /**
     * 记录连接关闭
     */
    public void recordConnectionClosed() {
        activeConnections.decrementAndGet();
    }

    /**
     * 更新QPS统计
     */
    public void updateQps() {
        long currentTime = System.currentTimeMillis();
        long currentRequests = totalRequests.sum();
        
        long timeDiff = currentTime - lastStatTime;
        if (timeDiff >= 1000) { // 每秒更新一次
            long requestDiff = currentRequests - lastRequestCount;
            long qps = requestDiff * 1000 / timeDiff;
            currentQps.set(qps);
            
            lastStatTime = currentTime;
            lastRequestCount = currentRequests;
        }
    }

    /**
     * 获取统计快照
     */
    public MetricsSnapshot getSnapshot() {
        updateQps();
        
        MetricsSnapshot snapshot = new MetricsSnapshot();
        snapshot.setTotalRequests(totalRequests.sum());
        snapshot.setSuccessRequests(successRequests.sum());
        snapshot.setFailedRequests(failedRequests.sum());
        snapshot.setActiveConnections(activeConnections.get());
        snapshot.setTotalConnections(totalConnections.sum());
        snapshot.setCurrentQps(currentQps.get());
        snapshot.setAverageResponseTime(averageResponseTime.get());
        snapshot.setMaxResponseTime(maxResponseTime.get());
        snapshot.setMinResponseTime(minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get());
        
        // 计算成功率
        long total = snapshot.getTotalRequests();
        if (total > 0) {
            snapshot.setSuccessRate((double) snapshot.getSuccessRequests() / total * 100);
        } else {
            snapshot.setSuccessRate(0.0);
        }
        
        return snapshot;
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        totalRequests.reset();
        successRequests.reset();
        failedRequests.reset();
        totalConnections.reset();
        currentQps.set(0);
        averageResponseTime.set(0);
        maxResponseTime.set(0);
        minResponseTime.set(Long.MAX_VALUE);
        totalResponseTime.reset();
        lastStatTime = System.currentTimeMillis();
        lastRequestCount = 0;
        log.info("NettyServerMetrics reset");
    }

    /**
     * 打印统计信息
     */
    public void logMetrics() {
        MetricsSnapshot snapshot = getSnapshot();
        log.info("=== Netty Server Metrics ===");
        log.info("Total Requests: {}", snapshot.getTotalRequests());
        log.info("Success Requests: {}", snapshot.getSuccessRequests());
        log.info("Failed Requests: {}", snapshot.getFailedRequests());
        log.info("Success Rate: {:.2f}%", snapshot.getSuccessRate());
        log.info("Active Connections: {}", snapshot.getActiveConnections());
        log.info("Total Connections: {}", snapshot.getTotalConnections());
        log.info("Current QPS: {}", snapshot.getCurrentQps());
        log.info("Average Response Time: {}ms", snapshot.getAverageResponseTime());
        log.info("Max Response Time: {}ms", snapshot.getMaxResponseTime());
        log.info("Min Response Time: {}ms", snapshot.getMinResponseTime());
        log.info("============================");
    }

    // 为了兼容 HealthController，添加便捷方法
    public void incrementTotalRequests() {
        totalRequests.increment();
    }

    public void incrementSuccessRequests() {
        successRequests.increment();
    }

    public void incrementErrorRequests() {
        failedRequests.increment();
    }

    public void incrementActiveConnections() {
        activeConnections.incrementAndGet();
        totalConnections.increment();
    }

    public void decrementActiveConnections() {
        activeConnections.decrementAndGet();
    }

    public void recordRequestDuration(long durationMs) {
        recordResponseTime(durationMs);
    }

    // Getter 方法
    public long getTotalRequests() {
        return totalRequests.sum();
    }

    public long getSuccessRequests() {
        return successRequests.sum();
    }

    public long getErrorRequests() {
        return failedRequests.sum();
    }

    public long getActiveConnections() {
        return activeConnections.get();
    }

    public long getTotalConnections() {
        return totalConnections.sum();
    }

    public long getAverageRequestDuration() {
        return averageResponseTime.get();
    }

    /**
     * 指标快照
     */
    @Data
    public static class MetricsSnapshot {
        private long totalRequests;
        private long successRequests;
        private long failedRequests;
        private double successRate;
        private long activeConnections;
        private long totalConnections;
        private long currentQps;
        private long averageResponseTime;
        private long maxResponseTime;
        private long minResponseTime;
    }

}