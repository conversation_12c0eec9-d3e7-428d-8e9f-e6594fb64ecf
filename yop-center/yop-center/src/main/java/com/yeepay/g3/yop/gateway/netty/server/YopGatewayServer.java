/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.server;

import com.yeepay.g3.yop.gateway.netty.config.NettyServerConfig;
import com.yeepay.g3.yop.gateway.netty.handler.YopGatewayHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.epoll.EpollServerSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.HttpServerExpectContinueHandler;
import io.netty.handler.codec.http.cors.CorsConfigBuilder;
import io.netty.handler.codec.http.cors.CorsHandler;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * title: YOP网关Netty服务器<br/>
 * description: 基于Netty实现的高性能YOP网关服务器，采用Reactor线程模型<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class YopGatewayServer {

    @Autowired
    private NettyServerConfig config;

    @Autowired
    private YopGatewayHandler gatewayHandler;

    /**
     * Boss EventLoopGroup - 用于接受连接
     */
    private EventLoopGroup bossGroup;

    /**
     * Worker EventLoopGroup - 用于处理I/O事件
     */
    private EventLoopGroup workerGroup;

    /**
     * 业务线程池 - 用于处理阻塞操作
     */
    private ExecutorService businessExecutor;

    /**
     * 服务器Channel
     */
    private Channel serverChannel;

    /**
     * 服务器运行状态
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 启动服务器 - 改进版本，避免阻塞容器启动
     *
     * @return 启动结果Future
     */
    @PostConstruct
    public CompletableFuture<Void> start() {
        if (running.compareAndSet(false, true)) {
            return doStart();
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 执行服务器启动 - 完全异步，不阻塞容器启动
     */
    private CompletableFuture<Void> doStart() {
        log.info("Starting YOP Gateway Server on port: {}", config.getPort());

        return CompletableFuture.supplyAsync(() -> {
            try {
                // 初始化线程组
                initEventLoopGroups();

                // 初始化业务线程池
                initBusinessExecutor();

                // 创建ServerBootstrap
                ServerBootstrap bootstrap = createServerBootstrap();

                // 绑定端口，但不等待
                ChannelFuture bindFuture = bootstrap.bind(config.getPort());

                // 等待绑定完成
                bindFuture.sync();
                serverChannel = bindFuture.channel();

                log.info("YOP Gateway Server started successfully on port: {}", config.getPort());

                // 添加关闭监听器，但不在这里等待
                serverChannel.closeFuture().addListener(future -> {
                    log.info("YOP Gateway Server channel closed");
                    running.set(false);
                });

                return null;

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Server startup interrupted", e);
                running.set(false);
                throw new RuntimeException("Server startup failed", e);
            } catch (Exception e) {
                log.error("Server startup failed", e);
                running.set(false);
                throw new RuntimeException("Server startup failed", e);
            }
        }).whenComplete((result, throwable) -> {
            if (throwable != null) {
                log.error("Failed to start YOP Gateway Server", throwable);
                // 清理资源
                cleanup();
            }
        });
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (serverChannel != null && serverChannel.isOpen()) {
                serverChannel.close();
            }

            if (bossGroup != null && !bossGroup.isShutdown()) {
                bossGroup.shutdownGracefully();
            }

            if (workerGroup != null && !workerGroup.isShutdown()) {
                workerGroup.shutdownGracefully();
            }

            if (businessExecutor != null && !businessExecutor.isShutdown()) {
                businessExecutor.shutdown();
            }
        } catch (Exception e) {
            log.warn("Error during cleanup", e);
        }
    }

    /**
     * 初始化EventLoopGroup
     */
    private void initEventLoopGroups() {
        // 检查是否支持Epoll (仅Linux系统)
        boolean useEpoll = config.isEpollEnabled() && isLinux();

        if (useEpoll) {
            log.info("Using Epoll transport for better performance");
            bossGroup = new EpollEventLoopGroup(config.getBossThreads(),
                    new DefaultThreadFactory("yop-boss", true));
            workerGroup = new EpollEventLoopGroup(config.getWorkerThreads(),
                    new DefaultThreadFactory("yop-worker", true));
        } else {
            log.info("Using NIO transport");
            bossGroup = new NioEventLoopGroup(config.getBossThreads(),
                    new DefaultThreadFactory("yop-boss", true));
            workerGroup = new NioEventLoopGroup(config.getWorkerThreads(),
                    new DefaultThreadFactory("yop-worker", true));
        }
    }

    /**
     * 初始化业务线程池
     */
    private void initBusinessExecutor() {
        businessExecutor = new ThreadPoolExecutor(
                config.getBusinessThreads(),
                config.getBusinessThreads(),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(config.getBusinessQueueSize()),
                new DefaultThreadFactory("yop-business", false),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        log.info("Business thread pool initialized with {} threads", config.getBusinessThreads());
    }

    /**
     * 创建ServerBootstrap
     */
    private ServerBootstrap createServerBootstrap() {
        ServerBootstrap bootstrap = new ServerBootstrap();
        
        // 设置EventLoopGroup
        bootstrap.group(bossGroup, workerGroup);

        // 设置Channel类型
        if (config.isEpollEnabled() && isLinux()) {
            bootstrap.channel(EpollServerSocketChannel.class);
        } else {
            bootstrap.channel(NioServerSocketChannel.class);
        }

        // 设置Channel选项
        bootstrap.option(ChannelOption.SO_BACKLOG, config.getSoBacklog())
                .option(ChannelOption.SO_REUSEADDR, config.isSoReuseAddr())
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT);

        // 设置子Channel选项
        bootstrap.childOption(ChannelOption.TCP_NODELAY, config.isTcpNoDelay())
                .childOption(ChannelOption.SO_KEEPALIVE, config.isSoKeepAlive())
                .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeoutMillis())
                .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT);

        // 设置ChannelInitializer
        bootstrap.childHandler(new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel ch) {
                setupPipeline(ch.pipeline());
            }
        });

        // 添加日志处理器
        if (log.isDebugEnabled()) {
            bootstrap.handler(new LoggingHandler(LogLevel.DEBUG));
        }

        return bootstrap;
    }

    /**
     * 设置Channel Pipeline
     */
    private void setupPipeline(ChannelPipeline pipeline) {
        // HTTP编解码器
        pipeline.addLast("http-codec", new HttpServerCodec());

        // HTTP消息聚合器
        pipeline.addLast("http-aggregator", 
                new HttpObjectAggregator(config.getMaxContentLength()));

        // HTTP期望继续处理器
        pipeline.addLast("http-expect-continue", new HttpServerExpectContinueHandler());

        // 分块写入处理器
        pipeline.addLast("chunked-writer", new ChunkedWriteHandler());

        // CORS处理器
        pipeline.addLast("cors", new CorsHandler(CorsConfigBuilder.forAnyOrigin()
                .allowNullOrigin()
                .allowCredentials()
                .build()));

        // 超时处理器
        pipeline.addLast("read-timeout", 
                new ReadTimeoutHandler(config.getReadTimeoutSeconds()));
        pipeline.addLast("write-timeout", 
                new WriteTimeoutHandler(config.getWriteTimeoutSeconds()));

        // 业务处理器
        pipeline.addLast("gateway-handler", gatewayHandler);
    }

    /**
     * 停止服务器
     */
    @PreDestroy
    public CompletableFuture<Void> stop() {
        if (running.compareAndSet(true, false)) {
            return doStop();
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 执行服务器停止
     */
    private CompletableFuture<Void> doStop() {
        log.info("Stopping YOP Gateway Server...");

        return CompletableFuture.runAsync(() -> {
            try {
                // 关闭服务器Channel
                if (serverChannel != null) {
                    serverChannel.close().sync();
                }

                // 优雅关闭线程组
                if (bossGroup != null) {
                    bossGroup.shutdownGracefully(0, config.getGracefulShutdownTimeoutSeconds(), TimeUnit.SECONDS).sync();
                }
                if (workerGroup != null) {
                    workerGroup.shutdownGracefully(0, config.getGracefulShutdownTimeoutSeconds(), TimeUnit.SECONDS).sync();
                }

                // 关闭业务线程池
                if (businessExecutor != null) {
                    businessExecutor.shutdown();
                    if (!businessExecutor.awaitTermination(config.getGracefulShutdownTimeoutSeconds(), TimeUnit.SECONDS)) {
                        businessExecutor.shutdownNow();
                    }
                }

                log.info("YOP Gateway Server stopped successfully");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Server shutdown interrupted", e);
            } catch (Exception e) {
                log.error("Error during server shutdown", e);
            }
        });
    }

    /**
     * 检查是否为Linux系统
     */
    private boolean isLinux() {
        return System.getProperty("os.name").toLowerCase().contains("linux");
    }

    /**
     * 获取业务线程池
     */
    public ExecutorService getBusinessExecutor() {
        return businessExecutor;
    }

    /**
     * 获取服务器运行状态
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 获取服务器配置
     */
    public NettyServerConfig getConfig() {
        return config;
    }

}