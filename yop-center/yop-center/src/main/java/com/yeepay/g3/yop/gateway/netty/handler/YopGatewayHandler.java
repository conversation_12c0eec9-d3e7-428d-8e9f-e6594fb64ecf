/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.handler;

import com.yeepay.boot.components.utils.UUIDExt;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.server.NettyServerWebExchange;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import io.netty.handler.timeout.ReadTimeoutException;
import io.netty.handler.timeout.WriteTimeoutException;
import io.netty.util.ReferenceCountUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.channels.ClosedChannelException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * title: YOP网关核心处理器<br/>
 * description: 处理HTTP请求的核心业务逻辑，将请求委托给过滤器链处理<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@Component
public class YopGatewayHandler extends SimpleChannelInboundHandler<FullHttpRequest> {

    @Setter
    private ExecutorService businessExecutor;

    @Setter
    private NettyServerMetrics metrics;

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) {
        // 生成请求ID
        String requestId = UUIDExt.compressV4UUID();

        // 获取远程地址
        String remoteAddress = ctx.channel().remoteAddress().toString();

        // 记录请求开始时间和指标
        long startTime = System.currentTimeMillis();
        if (metrics != null) {
            metrics.incrementActiveConnections();
            metrics.incrementTotalRequests();
        }

        // 记录请求日志
        log.info("Received request: {} {} from {} [{}]",
                request.method(), request.uri(), remoteAddress, requestId);

        // 检查业务线程池状态，实现背压控制
        if (businessExecutor.isShutdown()) {
            log.warn("Business executor is shutdown, rejecting request [{}]", requestId);
            handleException(ctx, new RejectedExecutionException("Server is shutting down"), requestId);
            safeReleaseRequest(request);
            return;
        }

        try {
            // 创建ServerWebExchange
            NettyServerWebExchange exchange = new NettyServerWebExchange(
                    request, ctx, requestId, remoteAddress, businessExecutor);

            // 异步处理请求
            CompletableFuture<FullHttpResponse> responseFuture = exchange.getResponseFuture();

            // 设置响应完成回调
            responseFuture.whenComplete((response, throwable) -> {
                try {
                    if (throwable != null) {
                        log.error("Request processing failed [{}]", requestId, throwable);
                        handleException(ctx, throwable, requestId);
                        if (metrics != null) {
                            metrics.incrementErrorRequests();
                        }
                    } else {
                        log.info("Request processed successfully [{}]", requestId);
                        ctx.writeAndFlush(response);
                        if (metrics != null) {
                            metrics.incrementSuccessRequests();
                        }
                    }
                } finally {
                    // 安全释放请求资源
                    safeReleaseRequest(request);

                    // 更新指标
                    if (metrics != null) {
                        metrics.decrementActiveConnections();
                        metrics.recordRequestDuration(System.currentTimeMillis() - startTime);
                    }
                }
            });

            // 在业务线程池中处理请求
            businessExecutor.submit(() -> {
                try {
                    processRequest(exchange);
                } catch (Exception e) {
                    log.error("Error processing request [{}]", requestId, e);
                    exchange.completeExceptionally(e);
                }
            });

        } catch (Exception e) {
            log.error("Error setting up request processing [{}]", requestId, e);
            handleException(ctx, e, requestId);
            safeReleaseRequest(request);
            if (metrics != null) {
                metrics.decrementActiveConnections();
                metrics.incrementErrorRequests();
            }
        }
    }

    /**
     * 处理请求
     */
    private void processRequest(NettyServerWebExchange exchange) {
        try {
            // TODO: 这里将集成现有的过滤器链处理逻辑
            // 暂时返回简单响应
            FullHttpResponse response = createSimpleResponse();
            exchange.completeResponse(response);
        } catch (Exception e) {
            exchange.completeExceptionally(e);
        }
    }

    /**
     * 创建简单响应
     */
    private FullHttpResponse createSimpleResponse() {
        String content = "{\"code\":\"SUCCESS\",\"message\":\"YOP Gateway is running\"}";
        FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, 
                HttpResponseStatus.OK,
                Unpooled.wrappedBuffer(content.getBytes())
        );
        
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
        response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);
        
        return response;
    }

    /**
     * 安全释放请求资源
     */
    private void safeReleaseRequest(FullHttpRequest request) {
        try {
            if (request != null && request.refCnt() > 0) {
                ReferenceCountUtil.release(request);
                log.debug("Request resources released successfully");
            }
        } catch (Exception e) {
            log.warn("Error releasing request resources", e);
        }
    }

    /**
     * 处理异常 - 改进版本，支持不同异常类型的分类处理
     */
    private void handleException(ChannelHandlerContext ctx, Throwable throwable, String requestId) {
        if (!ctx.channel().isActive()) {
            log.debug("Channel is not active, skipping error response [{}]", requestId);
            return;
        }

        try {
            HttpResponseStatus status;
            String errorCode;
            String errorMessage;

            // 根据异常类型进行分类处理
            if (throwable instanceof TimeoutException || throwable instanceof ReadTimeoutException) {
                status = HttpResponseStatus.REQUEST_TIMEOUT;
                errorCode = "REQUEST_TIMEOUT";
                errorMessage = "Request timeout";
                log.warn("Request timeout [{}]", requestId, throwable);
            } else if (throwable instanceof WriteTimeoutException) {
                status = HttpResponseStatus.REQUEST_TIMEOUT;
                errorCode = "RESPONSE_TIMEOUT";
                errorMessage = "Response timeout";
                log.warn("Response timeout [{}]", requestId, throwable);
            } else if (throwable instanceof IllegalArgumentException) {
                status = HttpResponseStatus.BAD_REQUEST;
                errorCode = "INVALID_PARAMETER";
                errorMessage = "Invalid parameter: " + throwable.getMessage();
                log.warn("Invalid parameter [{}]", requestId, throwable);
            } else if (throwable instanceof RejectedExecutionException) {
                status = HttpResponseStatus.SERVICE_UNAVAILABLE;
                errorCode = "SERVICE_UNAVAILABLE";
                errorMessage = "Service temporarily unavailable";
                log.warn("Service unavailable [{}]", requestId, throwable);
            } else if (throwable instanceof ClosedChannelException) {
                // 连接已关闭，不需要发送响应
                log.debug("Channel closed, no response needed [{}]", requestId);
                return;
            } else {
                status = HttpResponseStatus.INTERNAL_SERVER_ERROR;
                errorCode = "INTERNAL_ERROR";
                errorMessage = "Internal server error";
                log.error("Internal server error [{}]", requestId, throwable);
            }

            // 构造错误响应
            String errorContent = String.format(
                "{\"code\":\"%s\",\"message\":\"%s\",\"requestId\":\"%s\"}",
                errorCode, errorMessage, requestId);

            FullHttpResponse errorResponse = new DefaultFullHttpResponse(
                    HttpVersion.HTTP_1_1,
                    status,
                    Unpooled.wrappedBuffer(errorContent.getBytes("UTF-8"))
            );

            errorResponse.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
            errorResponse.headers().set(HttpHeaderNames.CONTENT_LENGTH, errorResponse.content().readableBytes());
            errorResponse.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);

            // 异步发送响应并关闭连接
            ctx.writeAndFlush(errorResponse).addListener(future -> {
                if (!future.isSuccess()) {
                    log.error("Failed to send error response [{}]", requestId, future.cause());
                }
                ctx.close();
            });

        } catch (Exception e) {
            log.error("Error sending error response [{}]", requestId, e);
            ctx.close();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("Channel exception caught from {}", ctx.channel().remoteAddress(), cause);
        ctx.close();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.debug("Channel active: {}", ctx.channel().remoteAddress());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.debug("Channel inactive: {}", ctx.channel().remoteAddress());
        super.channelInactive(ctx);
    }

}