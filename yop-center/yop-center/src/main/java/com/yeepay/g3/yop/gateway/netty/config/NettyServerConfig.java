/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * title: Netty服务器配置<br/>
 * description: 配置Netty服务器的各种参数，包括线程池、网络参数等<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Data
@Component
@ConfigurationProperties(prefix = "yop.gateway.netty")
public class NettyServerConfig {

    /**
     * 服务器端口
     */
    private int port = 8080;

    /**
     * Boss线程组大小，用于接受连接
     */
    private int bossThreads = 1;

    /**
     * Worker线程组大小，用于处理I/O事件
     * 默认为CPU核数*2
     */
    private int workerThreads = Runtime.getRuntime().availableProcessors() * 2;

    /**
     * 业务线程池核心线程数，用于处理阻塞操作
     * 默认为CPU核数*4
     */
    private int businessThreads = Runtime.getRuntime().availableProcessors() * 4;

    /**
     * 业务线程池最大线程数
     * 默认为核心线程数的2倍
     */
    private int businessMaxThreads = Runtime.getRuntime().availableProcessors() * 8;

    /**
     * 业务线程池队列大小
     */
    private int businessQueueSize = 10000;

    /**
     * 业务线程空闲保持时间（秒）
     */
    private long businessThreadKeepAliveSeconds = 60L;

    /**
     * 是否允许核心线程超时
     */
    private boolean allowCoreThreadTimeout = true;

    /**
     * SO_BACKLOG参数，TCP全连接队列大小
     */
    private int soBacklog = 1024;

    /**
     * SO_REUSEADDR参数
     */
    private boolean soReuseAddr = true;

    /**
     * TCP_NODELAY参数，禁用Nagle算法
     */
    private boolean tcpNoDelay = true;

    /**
     * SO_KEEPALIVE参数
     */
    private boolean soKeepAlive = true;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeoutMillis = 30000;

    /**
     * 读超时时间（秒）
     */
    private int readTimeoutSeconds = 60;

    /**
     * 写超时时间（秒）
     */
    private int writeTimeoutSeconds = 60;

    /**
     * 最大内容长度（字节）
     */
    private int maxContentLength = 10 * 1024 * 1024; // 10MB

    /**
     * 是否启用压缩
     */
    private boolean compressionEnabled = true;

    /**
     * 压缩阈值（字节）
     */
    private int compressionThreshold = 1024;

    /**
     * 是否使用堆外内存
     */
    private boolean useDirectMemory = true;

    /**
     * 是否启用Epoll（仅Linux系统）
     */
    private boolean epollEnabled = true;

    /**
     * 优雅关闭超时时间（秒）
     */
    private int gracefulShutdownTimeoutSeconds = 10;

    /**
     * 是否启用性能监控
     */
    private boolean metricsEnabled = true;

    /**
     * 监控统计周期（秒）
     */
    private int metricsIntervalSeconds = 30;

}