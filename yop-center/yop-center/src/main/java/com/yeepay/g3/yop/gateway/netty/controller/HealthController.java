/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.netty.controller;

import com.yeepay.g3.yop.gateway.async.AsyncFilterChainMetrics;
import com.yeepay.g3.yop.gateway.netty.metrics.NettyServerMetrics;
import com.yeepay.g3.yop.gateway.netty.server.YopGatewayServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadMXBean;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * title: 健康检查控制器<br/>
 * description: 提供系统健康状态、性能指标和监控信息的REST接口<br/>
 * Copyright: Copyright (c) 2024<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/1/1 00:00
 */
@Slf4j
@RestController
@RequestMapping("/actuator")
public class HealthController {

    @Autowired
    private YopGatewayServer gatewayServer;

    @Autowired
    private NettyServerMetrics nettyServerMetrics;

    /**
     * 基础健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            boolean isRunning = gatewayServer.isRunning();
            health.put("status", isRunning ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            if (isRunning) {
                health.put("details", getBasicHealthDetails());
                return ResponseEntity.ok(health);
            } else {
                health.put("error", "Gateway server is not running");
                return ResponseEntity.status(503).body(health);
            }
            
        } catch (Exception e) {
            log.error("Error checking health", e);
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ResponseEntity.status(503).body(health);
        }
    }

    /**
     * 详细的系统指标
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> metrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // Netty服务器指标
            if (nettyServerMetrics != null) {
                metrics.put("netty", getNettyMetrics());
            }
            
            // 异步过滤器链指标
            AsyncFilterChainMetrics filterMetrics = AsyncFilterChainMetrics.getInstance();
            metrics.put("filterChain", getFilterChainMetrics(filterMetrics));
            
            // 线程池指标
            metrics.put("threadPool", getThreadPoolMetrics());
            
            // JVM指标
            metrics.put("jvm", getJvmMetrics());
            
            metrics.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(metrics);
            
        } catch (Exception e) {
            log.error("Error collecting metrics", e);
            Map<String, Object> error = new HashMap<>();
            error.put("error", e.getMessage());
            error.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(error);
        }
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("application", "YOP Gateway");
        info.put("version", "2.0.0");
        info.put("description", "High-performance API Gateway based on Netty");
        info.put("java.version", System.getProperty("java.version"));
        info.put("java.vendor", System.getProperty("java.vendor"));
        info.put("os.name", System.getProperty("os.name"));
        info.put("os.version", System.getProperty("os.version"));
        info.put("os.arch", System.getProperty("os.arch"));
        info.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(info);
    }

    /**
     * 获取基础健康详情
     */
    private Map<String, Object> getBasicHealthDetails() {
        Map<String, Object> details = new HashMap<>();
        
        // 服务器状态
        details.put("server.running", gatewayServer.isRunning());
        details.put("server.port", gatewayServer.getConfig().getPort());
        
        // 基础指标
        if (nettyServerMetrics != null) {
            details.put("connections.active", nettyServerMetrics.getActiveConnections());
            details.put("requests.total", nettyServerMetrics.getTotalRequests());
            details.put("requests.success", nettyServerMetrics.getSuccessRequests());
            details.put("requests.error", nettyServerMetrics.getErrorRequests());
        }
        
        return details;
    }

    /**
     * 获取Netty指标
     */
    private Map<String, Object> getNettyMetrics() {
        Map<String, Object> nettyMetrics = new HashMap<>();
        
        nettyMetrics.put("activeConnections", nettyServerMetrics.getActiveConnections());
        nettyMetrics.put("totalConnections", nettyServerMetrics.getTotalConnections());
        nettyMetrics.put("totalRequests", nettyServerMetrics.getTotalRequests());
        nettyMetrics.put("successRequests", nettyServerMetrics.getSuccessRequests());
        nettyMetrics.put("errorRequests", nettyServerMetrics.getErrorRequests());
        nettyMetrics.put("averageRequestDuration", nettyServerMetrics.getAverageRequestDuration());
        
        return nettyMetrics;
    }

    /**
     * 获取过滤器链指标
     */
    private Map<String, Object> getFilterChainMetrics(AsyncFilterChainMetrics filterMetrics) {
        Map<String, Object> chainMetrics = new HashMap<>();
        
        AsyncFilterChainMetrics.SystemStats systemStats = filterMetrics.getSystemStats();
        chainMetrics.put("activeRequests", systemStats.getActiveRequests());
        chainMetrics.put("queuedRequests", systemStats.getQueuedRequests());
        chainMetrics.put("rejectedRequests", systemStats.getRejectedRequests());
        chainMetrics.put("parallelExecutionCount", systemStats.getParallelExecutionCount());
        chainMetrics.put("serialExecutionCount", systemStats.getSerialExecutionCount());
        chainMetrics.put("parallelExecutionRatio", systemStats.getParallelExecutionRatio());
        chainMetrics.put("overloaded", systemStats.isOverloaded());
        
        return chainMetrics;
    }

    /**
     * 获取线程池指标
     */
    private Map<String, Object> getThreadPoolMetrics() {
        Map<String, Object> threadPoolMetrics = new HashMap<>();
        
        ExecutorService businessExecutor = gatewayServer.getBusinessExecutor();
        if (businessExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) businessExecutor;
            threadPoolMetrics.put("corePoolSize", tpe.getCorePoolSize());
            threadPoolMetrics.put("maximumPoolSize", tpe.getMaximumPoolSize());
            threadPoolMetrics.put("activeCount", tpe.getActiveCount());
            threadPoolMetrics.put("poolSize", tpe.getPoolSize());
            threadPoolMetrics.put("queueSize", tpe.getQueue().size());
            threadPoolMetrics.put("completedTaskCount", tpe.getCompletedTaskCount());
            threadPoolMetrics.put("taskCount", tpe.getTaskCount());
            threadPoolMetrics.put("isShutdown", tpe.isShutdown());
            threadPoolMetrics.put("isTerminated", tpe.isTerminated());
        }
        
        return threadPoolMetrics;
    }

    /**
     * 获取JVM指标
     */
    private Map<String, Object> getJvmMetrics() {
        Map<String, Object> jvmMetrics = new HashMap<>();
        
        // 内存信息
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        jvmMetrics.put("memory.heap.used", memoryBean.getHeapMemoryUsage().getUsed());
        jvmMetrics.put("memory.heap.max", memoryBean.getHeapMemoryUsage().getMax());
        jvmMetrics.put("memory.heap.committed", memoryBean.getHeapMemoryUsage().getCommitted());
        jvmMetrics.put("memory.nonheap.used", memoryBean.getNonHeapMemoryUsage().getUsed());
        jvmMetrics.put("memory.nonheap.max", memoryBean.getNonHeapMemoryUsage().getMax());
        
        // 线程信息
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        jvmMetrics.put("threads.live", threadBean.getThreadCount());
        jvmMetrics.put("threads.daemon", threadBean.getDaemonThreadCount());
        jvmMetrics.put("threads.peak", threadBean.getPeakThreadCount());
        
        // 运行时信息
        Runtime runtime = Runtime.getRuntime();
        jvmMetrics.put("processors", runtime.availableProcessors());
        jvmMetrics.put("uptime", ManagementFactory.getRuntimeMXBean().getUptime());
        
        return jvmMetrics;
    }
}
